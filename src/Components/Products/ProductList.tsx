import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  productItemFullFetched,
  productItemPartialFetched,
} from "../constants";
import { useNavigate } from "react-router-dom";
import { getProducts } from "../../store/actioncreators/productactions";
import { spinnerLoaderStart } from "../../store/actioncreators/loaderactions";
import { ScreenSpinner } from "../utils/loader";
import RenderHTML from "../utils/RenderHTML";
import biotechnology from "../../assests/images/biotechnology.jpg";
import cleantech from "../../assests/images/cleantech.jpg";
import informationTechnology from "../../assests/images/information-technology.jpg";
// import { InteractiveCard } from "../ui";
// import "../../styles/animations.css";

// Modern Technology Card Component
const Card = ({ item }: { item: productItemFullFetched }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    dispatch(spinnerLoaderStart());
    navigate(`/product/${item._id}`, { state: { product: item } });
  };

  const getDefaultImage = (sectorName: string) => {
    if (sectorName === "Biotechnology") {
      return biotechnology;
    } else if (sectorName === "Clean Technology") {
      return cleantech;
    } else {
      return informationTechnology;
    }
  };

  const getSectorColor = (sectorName: string) => {
    switch (sectorName) {
      case "Biotechnology":
        return "bg-green-100 text-green-800";
      case "Clean Technology":
        return "bg-blue-100 text-blue-800";
      case "Information Technology":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const DOP = new Date(item.createdAt);

  return (
    <article
      className="product-card-main group"
      style={{ opacity: 1, visibility: "visible" }}
      onClick={handleView}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleView();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${item.name} technology`}
    >
      {/* Image Section with Overlay */}
      <div className="product-card-img relative">
        <img
          src={
            item.image
              ? item.image
              : item.company.logo
              ? item.company.logo
              : getDefaultImage(item.sectors.name)
          }
          className="w-full h-48 object-cover rounded-t-2xl"
          alt={`${item.name} technology preview`}
          loading="lazy"
        />

        {/* Sector Badge */}
        <div className="absolute top-3 left-3">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSectorColor(
              item.sectors.name
            )}`}
          >
            {item.sectors.name}
          </span>
        </div>

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-t-2xl flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="bg-white rounded-full p-3 shadow-lg">
              <svg
                className="w-6 h-6 text-GTI-BLUE-default"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-roboto text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
              {item.name}
            </h3>
            {item.company?.name && (
              <p className="text-sm text-gray-600 mt-1 font-medium">
                {item.company.name}
              </p>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="flex-1 mb-4">
          <div className="text-sm text-gray-600 line-clamp-3">
            <RenderHTML
              html={
                item.description.substring(0, 120) +
                (item.description.length > 120 ? "..." : "")
              }
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center text-xs text-gray-500">
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            {DOP.toLocaleString("default", {
              month: "short",
              day: "2-digit",
              year: "numeric",
            })}
          </div>

          <div className="flex items-center text-GTI-BLUE-default text-sm font-medium">
            View Details
            <svg
              className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </div>
    </article>
  );
};

const ProductList = ({
  skip,
  limit,
  secId,
  subSecId,
  search,
  sortBy = "newest",
  developmentStageFilter = "",
  dateRange = { from: "", to: "" },
}: {
  skip: string;
  limit: string;
  subSecId: string;
  secId: string;
  search: string;
  sortBy?: string;
  developmentStageFilter?: string;
  dateRange?: { from: string; to: string };
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  const products: PRODUCTS = useSelector(
    (state: STATE) => state.PRODUCTS.PRODUCTS
  );

  const navigate = useNavigate();
  const [loadMore, setMore] = useState(false);

  useEffect(() => {
    dispatch(getProducts(skip, limit, secId, subSecId, search));
  }, [secId, subSecId, skip, limit, search]);

  // Loading Skeleton Component
  const LoadingSkeleton = () => (
    <div className="product-card-main animate-pulse">
      <div className="h-48 bg-gray-200 rounded-t-2xl"></div>
      <div className="p-6">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          <div className="h-3 bg-gray-200 rounded w-4/6"></div>
        </div>
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
          <div className="h-3 bg-gray-200 rounded w-20"></div>
          <div className="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>
  );

  // Empty State Component
  const EmptyState = () => (
    <div className="col-span-full flex flex-col items-center justify-center py-16">
      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
        <svg
          className="w-12 h-12 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        No technologies found
      </h3>
      <p className="text-gray-600 text-center max-w-md">
        We couldn't find any technologies matching your criteria. Try adjusting
        your filters or search terms.
      </p>
    </div>
  );

  // Enhanced filtering and sorting logic
  const filteredAndSortedProducts = React.useMemo(() => {
    let filtered =
      products.PRODUCTS_LIST?.products?.filter(
        (item: productItemPartialFetched) => {
          // Existing filters
          const sectorMatch =
            subSecId !== "" ? subSecId === item.subSectorId : true;

          // Development stage filter
          const stageMatch =
            developmentStageFilter !== ""
              ? item.developmentStage === developmentStageFilter
              : true;

          // Date range filter
          let dateMatch = true;
          if (dateRange.from || dateRange.to) {
            const itemDate = new Date(item.createdAt);
            const fromDate = dateRange.from
              ? new Date(dateRange.from)
              : new Date("1900-01-01");
            const toDate = dateRange.to ? new Date(dateRange.to) : new Date();
            dateMatch = itemDate >= fromDate && itemDate <= toDate;
          }

          return sectorMatch && stageMatch && dateMatch;
        }
      ) || [];

    // Sorting logic
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "oldest":
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case "name":
          return a.name.localeCompare(b.name);
        case "relevance":
          // Simple relevance based on search term matches
          if (search) {
            const aRelevance =
              (a.name.toLowerCase().includes(search.toLowerCase()) ? 2 : 0) +
              (a.description.toLowerCase().includes(search.toLowerCase())
                ? 1
                : 0);
            const bRelevance =
              (b.name.toLowerCase().includes(search.toLowerCase()) ? 2 : 0) +
              (b.description.toLowerCase().includes(search.toLowerCase())
                ? 1
                : 0);
            return bRelevance - aRelevance;
          }
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "newest":
        default:
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
      }
    });

    return sorted;
  }, [
    products.PRODUCTS_LIST?.products,
    subSecId,
    developmentStageFilter,
    dateRange,
    sortBy,
    search,
  ]);

  // Ensure data stability
  const isLoading = spinner.SPINNER;
  const hasProducts = filteredAndSortedProducts.length > 0;

  return (
    <div className="w-full min-h-[400px]">
      {isLoading ? (
        <div className="product-list-main">
          {Array.from({ length: 6 }, (_, index) => (
            <LoadingSkeleton key={index} />
          ))}
        </div>
      ) : (
        <div className="product-list-main">
          {hasProducts ? (
            filteredAndSortedProducts.map(
              (item: productItemFullFetched, id: number) => {
                // Ensure item has required properties
                if (!item || !item._id) return null;

                return <Card item={item} key={`${item._id}-${id}`} />;
              }
            )
          ) : (
            <div className="animate-fade-in-up">
              <EmptyState />
            </div>
          )}
        </div>
      )}
    </div>
  );
};
export default ProductList;
