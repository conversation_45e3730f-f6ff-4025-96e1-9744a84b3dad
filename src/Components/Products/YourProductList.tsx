import React, { Dispatch, useEffect, useState, useRef } from "react";
import ReactDOM from "react-dom";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import productbanner from "../../assests/images/product_alt.png";
import {
  APPROVED,
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  developmentStage,
  FILE_PATH,
  FILE_TYPE,
  iprStatus,
  NONE,
  PENDING,
  presignedData,
  productItemFullFetched,
  productItemPartialFetched,
  productUpdateItem,
  REJECTED,
  sectorItem,
  subsectorItem,
  YOUR_PRODUCT,
} from "../constants";
import { useNavigate } from "react-router-dom";
import {
  getYourProducts,
  updateProduct,
} from "../../store/actioncreators/productactions";
import { MdOutlineEdit } from "react-icons/md";
import axios from "axios";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { Formik, Form } from "formik";
import { productSchema } from "../validations/productValidations";

import RenderHTML from "../utils/RenderHTML";
import biotechnology from "../../assests/images/biotechnology.jpg";
import cleantech from "../../assests/images/cleantech.jpg";
import informationTechnology from "../../assests/images/information-technology.jpg";

import { notify } from "../../utils";
import { FiTrash2 } from "react-icons/fi";
import CustomEditor from "../shared/CustomEditor";

interface MyFormValues {
  name: string;
  description: string;
  sectorId: string;
  subSectorId: string;
  developmentStage: string;
  iprStatus: string[];
}

type files = {
  image: Boolean;
  document: Boolean;
  imageFile: File | null;
  video: any;
  documentFiles: FileList | null;
};

const ProductModal = ({
  currentProduct,
  handleModal,
}: {
  currentProduct: productItemPartialFetched;
  handleModal: () => void;
}) => {
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );
  const docInputRef = useRef<HTMLInputElement>(null);
  const vidInputRef = useRef<HTMLInputElement>(null);

  const initialValues: MyFormValues = {
    name: currentProduct.name,
    description: currentProduct.description,
    sectorId: currentProduct.sectorId,
    subSectorId: currentProduct.subSectorId,
    developmentStage: currentProduct.developmentStage,
    iprStatus: currentProduct.iprStatus,
  };

  const dispatch: Dispatch<any> = useDispatch();

  const [iprStatusCheckbox, setIprStatusCheckbox] = useState(
    getInitialValueForIprCheckbox()
  );

  function handleIprStatus(id: Number) {
    const updatedOptions = iprStatusCheckbox.map((option) => {
      if (option.id === id) {
        return { ...option, checked: !option.checked };
      }
      return option;
    });
    setIprStatusCheckbox(updatedOptions);
  }

  function getIprStatus() {
    const iprStatus = [];

    for (let i = 0; i < iprStatusCheckbox.length; i++) {
      if (iprStatusCheckbox[i].checked) {
        iprStatus.push(iprStatusCheckbox[i].value);
      }
    }

    return iprStatus;
  }

  function getInitialValueForIprCheckbox() {
    const updatedOptions = iprStatus.map((option) => {
      if (
        initialValues.iprStatus.findIndex(
          (status) => status === option.value
        ) >= 0
      ) {
        return { ...option, checked: !option.checked };
      }
      return option;
    });

    return updatedOptions;
  }

  const [files, setFiles] = useState<files>({
    image: false,
    document: false,
    imageFile: null,
    documentFiles: null,
    video: null,
  });

  const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setFiles({ ...files, imageFile: fileList[0], image: false });
    // files.imageFile = fileList;
  };

  const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;
    // initialValues.document = fileList
    setFiles({ ...files, documentFiles: fileList, document: false });
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        // console.log("url fetched", response);
        result = response.data;
      })
      .catch(function (error) {
        // console.log("Error", error);
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: files.imageFile,
    };

    await axios(config)
      .then(function (response) {
        // console.log("logo uploaded");
        dispatch(successToast());
      })
      .catch(function (error) {
        // console.log("error uploading logo");
      });
  };

  const postDocument = async (signed: string, file: File) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE_DOC,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };

    await axios(config)
      .then(function (response) {
        // console.log("document uploaded");
        dispatch(successToast());
      })
      .catch(function (error) {
        // console.log("error uploading document");
      });
  };
  const handleVideo = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e?.target.files ? e.target.files[0] : null;
    if (!file) return;
    const fileSizeInMB = file.size / (1024 * 1024); // Convert file size to MB
    if (fileSizeInMB <= 50) {
      setFiles((prev) => ({ ...prev, video: file }));
    } else {
      alert("Please select a file smaller than 50 MB.");
    }
  };
  const uploadVideo = async (selectedFile: any, presignedUrl: string) => {
    if (!selectedFile) {
      return;
    }

    try {
      await axios.put(presignedUrl, selectedFile, {
        headers: {
          "Content-Type": selectedFile.type,
          "Access-Control-Allow-Origin": true,
        },
      });

      // Perform any necessary actions upon successful upload
    } catch (error) {
      console.error("Error occurred during file upload:", error);
      notify("Failed to upload video", "error");
      // Handle any errors that occurred during the upload
    }
  };

  const handleCreate = async (values: MyFormValues) => {
    let signedLogoURL: string = "";
    let signedDocumentURLWhole: string = "";
    let videoUrl: string = "";

    // if (!productModal.image?.size && !productModal.document) return;

    if (files.imageFile) {
      const signedLogoData: presignedData = {
        fileName: files.imageFile.name || values.name,
        filePath: FILE_PATH.PRODUCTS_IMAGE,
        fileType: FILE_TYPE.PNG,
      };
      signedLogoURL = await getPresigned(signedLogoData);
      await postLogo(signedLogoURL);
    }

    const videoData: presignedData = {
      fileName: files?.video?.name ?? values.name,
      filePath: FILE_PATH.PRODUCTS_VIDEO,
      fileType: files?.video?.type ?? "",
    };
    if (files?.video) {
      videoUrl = await getPresigned(videoData);
      await uploadVideo(files.video, videoUrl);
    }

    if (files.documentFiles) {
      Array.from(files.documentFiles).forEach(async (document, i) => {
        let signedDocumentData: presignedData = {
          fileName: document.name || values.name,
          filePath: FILE_PATH.COMPANY_DOCS,
          fileType: FILE_TYPE.PDF,
        };
        let tempurl = (await getPresigned(signedDocumentData)) + " ";
        signedDocumentURLWhole = tempurl.split("?")[0] + " ";
        postDocument(tempurl, document);
        // signedDocumentURL.push(tempurl);
      });
    }
    // setTimeout(() => {
    const data: productUpdateItem = {
      productId: currentProduct._id,
      name: values.name,
      description: values.description,
      document: files.documentFiles
        ? signedDocumentURLWhole
        : currentProduct.document,
      image: files.imageFile
        ? signedLogoURL.split("?")[0]
        : currentProduct.image,
      sectorId: values.sectorId,
      subSectorId: values.subSectorId,
      developmentStage: values.developmentStage,
      iprStatus: getIprStatus(),
    };
    if (videoUrl) {
      data.video = videoUrl.split("?")[0];
    }
    dispatch(updateProduct(data));
    handleModal();
    // }, 2000);
  };

  const content = (
    <div className="z-10 pb-[200px] pt-4 fixed w-full h-screen bg-slate-700 bg-opacity-70 top-0 left-0 flex justify-center overflow-auto">
      <div className="product-modal-main relative">
        <div className="flex">
          <h4 className="text-lg font-roboto">Edit Product</h4>
          <button
            onClick={() => {
              handleModal();
            }}
            className="absolute right-0 top-0 font-bold hover:text-red-500 duration-300 border border-slate-100 px-3 py-1 rounded"
          >
            X
          </button>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={productSchema}
          onSubmit={(values) => handleCreate(values)}
        >
          {({ handleChange, setFieldValue, handleSubmit, errors, values }) => {
            return (
              <>
                <Form className="flex flex-col w-full space-y-4 justify-center items-center">
                  <div className="flex flex-col w-full space-x-2 relative">
                    <div className="relative">
                      <input
                        defaultValue={currentProduct.name}
                        onChange={(e) => setFieldValue("name", e.target.value)}
                        type="text"
                        id="floating_outlined"
                        className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                        placeholder=" "
                      />
                      <label
                        htmlFor="floating_outlined"
                        className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                      >
                        Product Name
                      </label>
                    </div>
                    {errors.name && (
                      <p
                        id="filled_error_help"
                        className="mt-2 text-xs text-red-600 dark:text-red-400"
                      >
                        {errors.name}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col w-full space-x-2 relative">
                    <div className="relative">
                      <CustomEditor
                        onChange={(content: string) => {
                          setFieldValue("description", content);
                        }}
                      />
                    </div>
                    {errors.description && (
                      <p
                        id="filled_error_help"
                        className="mt-2 text-xs text-red-600 dark:text-red-400"
                      >
                        {errors.description}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col w-full">
                    <div className="flex flex-row w-full space-x-5 items-center">
                      <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                        Sector Type:
                      </h3>
                      <select
                        onChange={(e) =>
                          setFieldValue("sectorId", e.target.value)
                        }
                        defaultValue={currentProduct.sectorId}
                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                      >
                        {sectorlist.SECTOR_LIST.map((item: sectorItem, id) => {
                          return <option value={item._id}>{item.name}</option>;
                        })}
                      </select>
                    </div>
                    {errors.sectorId && (
                      <p
                        id="filled_error_help"
                        className="mt-2 text-xs text-red-600 dark:text-red-400"
                      >
                        {errors.sectorId}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col w-full">
                    <div className="flex flex-row w-full space-x-5 items-center">
                      <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                        Sub Sector Type:
                      </h3>
                      <select
                        onChange={(e) =>
                          setFieldValue("subsecId", e.target.value)
                        }
                        defaultValue={currentProduct.subSectorId}
                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                      >
                        {subsectorlist.SUB_SECTOR_LIST.map(
                          (item: subsectorItem, id) => {
                            return (
                              <option value={item._id}>{item.name}</option>
                            );
                          }
                        )}
                      </select>
                    </div>
                    {errors.subSectorId && (
                      <p
                        id="filled_error_help"
                        className="mt-2 text-xs text-red-600 dark:text-red-400"
                      >
                        {errors.subSectorId}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col w-full">
                    <div className="flex flex-row w-full space-x-5 items-center">
                      <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                        Development Stage:
                      </h3>
                      <select
                        onChange={(e) =>
                          setFieldValue("developmentStage", e.target.value)
                        }
                        defaultValue={values.developmentStage}
                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                      >
                        {developmentStage.map((stage, id) => {
                          return (
                            <option value={stage.value}>{stage.value}</option>
                          );
                        })}
                      </select>
                    </div>
                  </div>
                  <div className="flex flex-col w-full space-x-2 relative">
                    <div className="relative">
                      <div className="relative mb-3">
                        <label className="profile-content-head-2">
                          IPR Status
                        </label>
                      </div>
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                        {iprStatusCheckbox.map((option) => (
                          <label
                            className="flex items-center space-x-2 ml-4"
                            key={option.id}
                          >
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
                              checked={option.checked}
                              onChange={() => handleIprStatus(option.id)}
                            />
                            <span className="text-gray-700 personal-input">
                              {option.value}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col w-full">
                    <label
                      className="block mb-2 text-sm font-medium text-gray-900"
                      htmlFor="logo"
                    >
                      Upload Product Image
                      <span className="text-red-500 text-xs">(.png only)</span>
                    </label>
                    <input
                      onChange={handleImage}
                      accept=".png"
                      type="file"
                      id="logo"
                      aria-label="company-logo"
                      className="modal-input"
                      placeholder="Click to upload Company's Logo"
                    />
                    <p
                      id="filled_error_help"
                      className={
                        "mt-2 text-xs text-red-600 dark:text-red-400 " +
                        (!files.image ? "hidden" : "")
                      }
                    >
                      {"Please upload product Image"}
                    </p>
                  </div>

                  <div className="flex flex-col w-full relative">
                    <label
                      className="block mb-2 text-sm font-medium text-gray-900"
                      htmlFor="documents"
                    >
                      Upload Product Documents <i>(Optional)</i>{" "}
                      <span className="text-red-500 text-xs">(.pdf only)</span>
                    </label>
                    <input
                      onChange={handleDocuments}
                      accept=".pdf"
                      type="file"
                      id="documents"
                      multiple
                      ref={docInputRef}
                      aria-label="company-documents"
                      className="modal-input"
                      placeholder="Click to upload Document"
                    />
                    <button
                      className="inline-flex items-center absolute border-2 p-2 rounded-md bg-white right-4 top-10"
                      onClick={() => {
                        setFiles((prev) => ({
                          ...prev,
                          documentFiles: null,
                          document: false,
                        }));
                        if (docInputRef.current) {
                          docInputRef.current.files = null;
                          docInputRef.current.value = "";
                        }
                      }}
                      type="button"
                    >
                      <FiTrash2 className="w-5 h-5" />
                    </button>
                  </div>
                  <div className="flex flex-col w-full relative">
                    <label
                      className="block mb-2 text-sm font-medium text-gray-900"
                      htmlFor="video"
                    >
                      Upload Product Video <i>(Optional)</i>{" "}
                      <span className="text-red-500 text-xs">
                        (.mp4 /.webm only)
                      </span>
                    </label>
                    <input
                      onChange={handleVideo}
                      type="file"
                      accept=".mp4, .webm"
                      id="video"
                      ref={vidInputRef}
                      aria-label="product-video"
                      className="modal-input"
                      placeholder="Click to upload Video "
                    />
                    <button
                      className="inline-flex items-center absolute border-2 p-2 rounded-md bg-white right-4 top-10"
                      onClick={() => {
                        setFiles((prev) => ({
                          ...prev,
                          video: null,
                        }));
                        if (vidInputRef.current) {
                          vidInputRef.current.files = null;
                          vidInputRef.current.value = "";
                        }
                      }}
                      type="button"
                    >
                      <FiTrash2 className="w-5 h-5" />
                    </button>
                  </div>
                  <button
                    type="submit"
                    onClick={() => handleSubmit}
                    className="button active"
                  >
                    Update
                  </button>
                </Form>
              </>
            );
          }}
        </Formik>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

const Card = ({
  item,
  handleSelect,
}: {
  item: productItemPartialFetched;
  handleSelect: (data: productItemPartialFetched) => void;
}) => {
  const navigate = useNavigate();

  const handleView = () => {
    navigate(YOUR_PRODUCT, { state: { product: item } });
  };

  const handleEdit = (e: any) => {
    e.stopPropagation();
    // Navigate to edit page with product ID
    navigate(`/edit-technology/${item._id}`);
  };

  const getDefaultImage = (sectorName: string) => {
    if (sectorName === "Biotechnology") {
      return biotechnology;
    } else if (sectorName === "Clean Technology") {
      return cleantech;
    } else {
      return informationTechnology;
    }
  };

  const getSectorColor = (sectorName: string) => {
    switch (sectorName) {
      case "Biotechnology":
        return "bg-green-100 text-green-800";
      case "Clean Technology":
        return "bg-blue-100 text-blue-800";
      case "Information Technology":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (item: productItemPartialFetched) => {
    if (item.isApprovedByAdmin) {
      return "bg-green-100 text-green-800";
    } else if (item.isRejected) {
      return "bg-red-100 text-red-800";
    } else {
      return "bg-yellow-100 text-yellow-800";
    }
  };

  const getStatusText = (item: productItemPartialFetched) => {
    if (item.isApprovedByAdmin) {
      return "Approved";
    } else if (item.isRejected) {
      return "Rejected";
    } else {
      return "Pending";
    }
  };

  const DOP = new Date(item.createdAt);

  return (
    <article
      className="product-card-main group"
      style={{ opacity: 1, visibility: "visible" }}
      onClick={handleView}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleView();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${item.name} technology`}
    >
      {/* Image Section with Overlay */}
      <div className="product-card-img relative">
        <img
          src={
            item.image === NONE
              ? productbanner
              : item.image ||
                getDefaultImage(item.sectors?.name || "Information Technology")
          }
          className="w-full h-48 object-cover rounded-t-2xl"
          alt={`${item.name} technology preview`}
          loading="lazy"
        />

        {/* Sector Badge */}
        <div className="absolute top-3 left-3">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSectorColor(
              item.sectors?.name || "Information Technology"
            )}`}
          >
            {item.sectors?.name || "Information Technology"}
          </span>
        </div>

        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
              item
            )}`}
          >
            {getStatusText(item)}
          </span>
        </div>

        {/* Edit Icon - Floating */}
        <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
          <button
            onClick={handleEdit}
            className="p-2.5 bg-white/90 backdrop-blur-sm text-gray-700 rounded-full shadow-lg hover:bg-white hover:shadow-xl transition-all duration-200 hover:scale-110"
            title="Edit Technology"
          >
            <MdOutlineEdit className="w-4 h-4" />
          </button>
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-t-2xl"></div>
      </div>

      {/* Content Section */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-roboto text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
              {item.name}
            </h3>
            <p className="text-sm text-gray-600 mt-1 font-medium">
              Your Technology
            </p>
          </div>

          {/* Edit Icon in Header */}
          <button
            onClick={handleEdit}
            className="p-2 text-gray-400 hover:text-GTI-BLUE-default hover:bg-blue-50 rounded-full transition-all duration-200"
            title="Edit Technology"
          >
            <MdOutlineEdit className="w-5 h-5" />
          </button>
        </div>

        {/* Description */}
        <div className="flex-1 mb-4">
          <div className="text-sm text-gray-600 line-clamp-3">
            <RenderHTML
              html={
                item.description.substring(0, 120) +
                (item.description.length > 120 ? "..." : "")
              }
            />
          </div>
        </div>

        {/* Development Stage Badge */}
        {item.developmentStage && (
          <div className="mb-4">
            <span className="inline-flex items-center px-2.5 py-1 bg-blue-50 text-blue-700 text-xs font-medium rounded-full border border-blue-200">
              {item.developmentStage}
            </span>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center gap-1.5">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <span className="font-medium">
                {DOP.toLocaleString("default", {
                  month: "short",
                  day: "2-digit",
                  year: "numeric",
                })}
              </span>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                item.isApprovedByAdmin
                  ? "bg-green-500"
                  : item.isRejected
                  ? "bg-red-500"
                  : "bg-yellow-500"
              }`}
            ></div>
            <span className="text-sm font-medium text-gray-600">
              {getStatusText(item)}
            </span>
          </div>
        </div>
      </div>
    </article>
  );
};

const YourProductList = ({
  type,
  skip,
  limit,
  secId,
  subsecId,
}: {
  type: number;
  skip: string;
  limit: string;
  subsecId: string;
  secId: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const products: PRODUCTS = useSelector(
    (state: STATE) => state.PRODUCTS.PRODUCTS
  );
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  useEffect(() => {
    switch (type) {
      case 0:
        dispatch(getYourProducts(skip, limit, APPROVED));
        break;
      case 1:
        dispatch(getYourProducts(skip, limit, PENDING));
        break;
      default:
        dispatch(getYourProducts(skip, limit, REJECTED));
    }
  }, [secId, subsecId, skip, type, dispatch, limit]);

  const [productEditModal, setModal] = useState(false);

  const [currentProduct, setProduct] = useState<productItemPartialFetched>({
    _id: "",
    name: "",
    description: "",
    image: "",
    displayOnHomePage: false,
    isApprovedBySubAdmin: false,
    isApprovedByAdmin: false,
    isRejected: false,
    document: "",
    sectorId: "",
    subSectorId: "",
    userId: "",
    createdAt: "",
    developmentStage: "",
    iprStatus: [],
    __v: -1,
    sectors: {
      _id: "",
      name: "",
      slug: "",
      image: "",
      createdAt: "",
      __v: -1,
    },
    subsectors: {
      _id: "",
      name: "",
      slug: "",
      sectorId: "",
      createdAt: "",
      __v: -1,
    },
  });

  const handleModel = () => {
    setModal(!productEditModal);
  };

  const handleSelect = (item: productItemPartialFetched) => {
    if (item.__v !== -1) {
      setProduct(item);
      handleModel();
    }
  };

  // Enhanced Empty State Component
  const EmptyState = () => (
    <div className="text-center py-20 animate-fadeInUp">
      <div className="relative mx-auto w-32 h-32 mb-8">
        {/* Animated Background Circle */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full animate-pulse"></div>
        <div className="absolute inset-2 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-full flex items-center justify-center">
          <svg
            className="w-16 h-16 text-blue-500 animate-bounce-subtle"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
            />
          </svg>
        </div>
        {/* Floating Particles */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60"></div>
        <div
          className="absolute -bottom-2 -left-2 w-3 h-3 bg-indigo-400 rounded-full animate-float opacity-40"
          style={{ animationDelay: "1s" }}
        ></div>
        <div
          className="absolute top-1/2 -right-4 w-2 h-2 bg-blue-300 rounded-full animate-float opacity-50"
          style={{ animationDelay: "0.5s" }}
        ></div>
      </div>

      <h3 className="text-3xl font-bold text-gray-900 mb-4 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
        No Technologies Found
      </h3>

      <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
        Ready to showcase your innovations? Create your first technology and
        join our global ecosystem of innovators and partners.
      </p>

      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <button
          onClick={() => window.location.reload()}
          className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 rounded-2xl transition-opacity duration-300"></div>
          <span className="relative flex items-center gap-2">
            <svg
              className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Refresh Page
          </span>
        </button>

        <button className="group px-8 py-4 border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700 font-semibold rounded-2xl hover:bg-blue-50 transition-all duration-300">
          <span className="flex items-center gap-2">
            <svg
              className="w-5 h-5 group-hover:scale-110 transition-transform duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Learn More
          </span>
        </button>
      </div>
    </div>
  );

  // Loading Skeleton Component
  const LoadingSkeleton = () => (
    <div className="product-list-main">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="premium-tech-card animate-pulse">
          <div className="h-56 bg-gradient-to-br from-gray-200 to-gray-300 rounded-t-3xl animate-shimmer"></div>
          <div className="p-8 space-y-4">
            <div className="flex justify-between items-start">
              <div className="h-4 bg-gray-200 rounded w-24 animate-shimmer"></div>
              <div className="h-8 w-8 bg-gray-200 rounded-full animate-shimmer"></div>
            </div>
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded w-3/4 animate-shimmer"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2 animate-shimmer"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-shimmer"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6 animate-shimmer"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6 animate-shimmer"></div>
            </div>
            <div className="h-8 bg-gray-200 rounded-xl w-32 animate-shimmer"></div>
            <div className="flex justify-between items-center pt-4">
              <div className="h-10 bg-gray-200 rounded-xl w-36 animate-shimmer"></div>
              <div className="flex gap-4">
                <div className="h-4 bg-gray-200 rounded w-16 animate-shimmer"></div>
                <div className="h-4 bg-gray-200 rounded w-16 animate-shimmer"></div>
              </div>
            </div>
          </div>
          <div className="h-1 bg-gray-200 animate-shimmer"></div>
        </div>
      ))}
    </div>
  );

  // Ensure data stability
  const productList = products.YOUR_PRODUCT_LIST?.products || [];
  const isLoading = spinner.SPINNER;
  const hasProducts = productList.length > 0;

  return (
    <div className="w-full min-h-[400px]">
      {isLoading ? (
        <LoadingSkeleton />
      ) : hasProducts ? (
        <div className="product-list-main">
          {productList.map((item: productItemFullFetched, id: number) => {
            // Ensure item has required properties
            if (!item || !item._id) return null;

            return (
              <Card
                item={item}
                key={`${item._id}-${id}`}
                handleSelect={handleSelect}
              />
            );
          })}
        </div>
      ) : (
        <EmptyState />
      )}

      {productEditModal && (
        <ProductModal
          currentProduct={currentProduct}
          handleModal={handleModel}
        />
      )}
    </div>
  );
};
export default YourProductList;
