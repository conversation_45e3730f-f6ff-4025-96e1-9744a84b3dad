import React, { Dispatch, useEffect, useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Helmet } from "react-helmet";

import {
  CREATE_TECHNOLOGY,
  LIMIT,
  productItemPartialFetched,
  sectorItem,
  SKIP,
  subsectorItem,
  TECHNOLOGY,
  title,
  metaData,
} from "../constants";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSectorBySector } from "../../store/actioncreators/sub-sectoractions";
import ProductList from "./ProductList";
import { useDetectClickOutside } from "react-detect-click-outside";
import product_logo from "../../assests/images/products_logo.png";
import { getQueryParams } from "../../utils";
import globe from "../../assests/home/<USER>";
import AdvancedSearch from "../Profile/AdvancedSearch";
import "./style.css";

// Modern icons for enhanced UI
const SearchIcon = () => (
  <svg
    className="w-5 h-5 text-gray-400"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const FilterIcon = () => (
  <svg
    className="w-5 h-5 text-gray-500"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
    />
  </svg>
);

const ChevronDownIcon = () => (
  <svg
    className="w-4 h-4 ml-2 transition-transform duration-200"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
);

const SortIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"
    />
  </svg>
);

const CalendarIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
    />
  </svg>
);

const AdjustmentsIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100-4m0 4v2m0-6V4"
    />
  </svg>
);

const XIcon = () => (
  <svg
    className="w-4 h-4"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M6 18L18 6M6 6l12 12"
    />
  </svg>
);

const Products = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const navigate = useNavigate();
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );
  const dispatch: Dispatch<any> = useDispatch();
  const navigator = useNavigate();

  const [page, setPage] = useState({
    skip: getQueryParams("skip") ? getQueryParams("skip") : SKIP,
    limit: LIMIT,
  });
  const [maxSkip, setMaxSkip] = useState(0);

  const [sector, setSector] = useState({
    drop: false,
    selected: "",
    id: "",
  });

  const [subSector, setSubSector] = useState({
    drop: false,
    selected: "",
    id: "",
    count: 0,
  });

  const ref1 = useDetectClickOutside({
    onTriggered: () => {
      setSector({ ...sector, drop: false });
    },
  });

  const ref2 = useDetectClickOutside({
    onTriggered: () =>
      setSubSector({
        drop: false,
        selected: "Select sub sector",
        id: "",
        count: 0,
      }),
  });

  const products: PRODUCTS = useSelector(
    (state: STATE) => state.PRODUCTS.PRODUCTS
  );

  const fetchData = (value: number) => {
    let final =
      parseInt(page.skip) + value < 0
        ? parseInt(page.skip)
        : parseInt(page.skip) + value;
    setPage({ skip: final.toString(), limit: page.limit });
    navigator(TECHNOLOGY + `?skip=${final}`);
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    setMaxSkip(
      Math.ceil(products.PRODUCTS_LIST.productsCount / parseInt(LIMIT))
    );
  }, [page, products]);

  useEffect(() => {
    dispatch(getSector());
    setSector({
      drop: false,
      selected: "All Sectors",
      id: "",
    });
    setSubSector({
      ...subSector,
      selected: "All Sub-Sectors",
      id: "",
      count: 0,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSectorClick = (id: string, name: string) => {
    if (id !== sector.id || sector.id === "") {
      dispatch(getSubSectorBySector(id));
      setSector({ ...sector, id: id, selected: name });
      setSubSector({ ...subSector, drop: true });
    } else {
      setSubSector({ ...subSector, drop: !subSector.drop });
    }
  };

  const handleSubSectorClick = () => {};

  const getCountbyFilter = () => {
    let count =
      products.PRODUCTS_LIST?.products &&
      products.PRODUCTS_LIST?.products?.reduce(
        (count: number, item: productItemPartialFetched) =>
          count + Number(item.subSectorId === subSector.id),
        0
      );
    return count;
  };

  // Enhanced search and filtering state
  const [search, setSearch] = useState("");
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [sortBy, setSortBy] = useState("newest"); // newest, oldest, name, relevance
  const [developmentStageFilter, setDevelopmentStageFilter] = useState("");
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [dateRange, setDateRange] = useState({ from: "", to: "" });
  const [savedSearches, setSavedSearches] = useState<string[]>([]);
  const [showSavedSearches, setShowSavedSearches] = useState(false);
  const [filteredResults, setFilteredResults] = useState<any[]>([]);
  const [useAdvancedSearch, setUseAdvancedSearch] = useState(false);

  // Transform products data for AdvancedSearch component
  const searchableProducts = useMemo(() => {
    if (!products?.PRODUCTS_LIST?.products) return [];

    return products.PRODUCTS_LIST.products.map((item: any) => ({
      id: item.id || item._id,
      title: item.name,
      description: item.description || "",
      category: item.sectorId || "Technology",
      status: item.developmentStage || "Active",
      date: new Date(item.createdAt || Date.now()),
      tags: [
        item.developmentStage,
        item.sectorId,
        item.subSectorId,
        item.company?.name,
        ...(item.keywords || []),
        ...(item.technologies || []),
      ].filter(Boolean),
      originalData: item,
    }));
  }, [products]);

  // Categories for filtering
  const categories = useMemo(() => {
    const sectors = sectorlist?.SECTOR_LIST || [];
    return sectors.map((sector: any) => ({
      value: sector.id,
      label: sector.name,
    }));
  }, [sectorlist]);

  // Status options for filtering
  const statuses = [
    { value: "Concept", label: "Concept" },
    { value: "Development", label: "Development" },
    { value: "Testing", label: "Testing" },
    { value: "Production", label: "Production" },
    { value: "Market Ready", label: "Market Ready" },
  ];

  // Available tags for filtering
  const availableTags = useMemo(() => {
    const allTags = searchableProducts.flatMap((item) => item.tags);
    return Array.from(new Set(allTags)).sort();
  }, [searchableProducts]);

  const handleSearchResults = (results: any[]) => {
    setFilteredResults(results);
  };

  // Initialize filtered results when searchable products change
  useEffect(() => {
    if (useAdvancedSearch) {
      setFilteredResults(searchableProducts);
    }
  }, [searchableProducts, useAdvancedSearch]);

  // Development stages for filtering
  const developmentStages = [
    "Concept",
    "Research & Development",
    "Prototype",
    "Testing",
    "Pre-Commercial",
    "Commercial",
    "Mature",
  ];

  const handleSearch = (searchValue: string) => {
    setPage({ skip: "0", limit: LIMIT });
    setSearch(searchValue);

    // Generate search suggestions based on existing products
    if (searchValue.length > 2) {
      const suggestions =
        products.PRODUCTS_LIST?.products
          ?.filter(
            (item) =>
              item.name.toLowerCase().includes(searchValue.toLowerCase()) ||
              item.company?.name
                ?.toLowerCase()
                .includes(searchValue.toLowerCase()) ||
              item.description.toLowerCase().includes(searchValue.toLowerCase())
          )
          .slice(0, 5)
          .map((item) => item.name) || [];

      setSearchSuggestions(Array.from(new Set(suggestions)));
      setShowSuggestions(suggestions.length > 0);
    } else {
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  };

  const handleSortChange = (sortOption: string) => {
    setSortBy(sortOption);
    setPage({ skip: "0", limit: LIMIT });
  };

  const clearAllFilters = () => {
    setSearch("");
    setSector({ drop: false, selected: "All Sectors", id: "" });
    setSubSector({
      drop: false,
      selected: "All Sub-Sectors",
      id: "",
      count: 0,
    });
    setDevelopmentStageFilter("");
    setDateRange({ from: "", to: "" });
    setSortBy("newest");
    setPage({ skip: "0", limit: LIMIT });
  };

  const saveCurrentSearch = () => {
    if (search && !savedSearches.includes(search)) {
      const newSavedSearches = [...savedSearches, search];
      setSavedSearches(newSavedSearches);
      localStorage.setItem(
        "savedTechSearches",
        JSON.stringify(newSavedSearches)
      );
    }
  };

  const loadSavedSearches = () => {
    const saved = localStorage.getItem("savedTechSearches");
    if (saved) {
      setSavedSearches(JSON.parse(saved));
    }
  };

  const removeSavedSearch = (searchToRemove: string) => {
    const newSavedSearches = savedSearches.filter((s) => s !== searchToRemove);
    setSavedSearches(newSavedSearches);
    localStorage.setItem("savedTechSearches", JSON.stringify(newSavedSearches));
  };

  // Load saved searches on component mount
  React.useEffect(() => {
    loadSavedSearches();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Helmet>
        <title>{title.TECHNOLOGY}</title>
        <meta
          name="description"
          key="description"
          content={metaData.TECHNOLOGY}
        />
        <meta name="title" key="title" content={title?.TECHNOLOGY} />
        <meta property="og:title" content={title.TECHNOLOGY} />
        <meta property="og:description" content={metaData.TECHNOLOGY} />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/technology`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={title?.TECHNOLOGY} />
        <meta name="twitter:description" content={metaData.TECHNOLOGY} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content={title?.TECHNOLOGY} />
      </Helmet>

      {/* Enhanced Hero Header Section */}
      <div className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800">
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}
          ></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
          <div className="text-center">
            {/* Animated Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-6 animate-fadeInUp">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
              Technology Showcase
            </div>

            {/* Main Title with Gradient */}
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 animate-fadeInUp delay-200">
              <span className="bg-gradient-to-r from-white via-blue-100 to-indigo-100 bg-clip-text text-transparent">
                Discover Technologies
              </span>
            </h1>

            {/* Enhanced Description */}
            <p className="text-xl sm:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed animate-fadeInUp delay-300">
              Explore innovative technologies from around the world and connect
              with groundbreaking solutions in our global technology ecosystem.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fadeInUp delay-500">
              <button
                onClick={() => navigate(CREATE_TECHNOLOGY)}
                className="group relative px-8 py-4 bg-white text-blue-600 font-semibold rounded-2xl hover:bg-blue-50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-2xl"
              >
                <span className="flex items-center gap-2">
                  <svg
                    className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create New Technology
                </span>
              </button>

              <button className="group px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-2xl hover:bg-white/10 backdrop-blur-sm transition-all duration-300">
                <span className="flex items-center gap-2">
                  <svg
                    className="w-5 h-5 group-hover:scale-110 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Learn More
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="relative bg-white/10 backdrop-blur-sm border-t border-white/20">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex justify-center items-center space-x-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">
                  {products.PRODUCTS_LIST?.productsCount || 0}
                </div>
                <div className="text-sm text-blue-100">Technologies</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">
                  {sectorlist?.SECTOR_LIST?.length || 0}
                </div>
                <div className="text-sm text-blue-100">Sectors</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Enhanced Filters and Search Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
          {/* Filter Header with Sort and Advanced Options */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FilterIcon />
                <span className="text-lg font-semibold text-gray-900">
                  Filters & Search
                </span>
              </div>

              {/* Results Count with Analytics */}
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600">
                  {products.PRODUCTS_LIST?.productsCount || 0} technologies
                  found
                </div>
                {search && (
                  <div className="text-xs text-GTI-BLUE-default bg-blue-50 px-2 py-1 rounded-full">
                    Searching for: "{search}"
                  </div>
                )}
              </div>
            </div>

            {/* Sort and Advanced Filters Toggle */}
            <div className="flex items-center space-x-3">
              {/* Sort Dropdown */}
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="name">Name A-Z</option>
                  <option value="relevance">Most Relevant</option>
                </select>
                <SortIcon />
              </div>

              {/* Advanced Filters Toggle */}
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  showAdvancedFilters
                    ? "bg-GTI-BLUE-default text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                <AdjustmentsIcon />
                <span>Advanced</span>
              </button>

              {/* Clear All Filters */}
              {(sector.id !== "" ||
                subSector.id !== "" ||
                search ||
                developmentStageFilter ||
                dateRange.from ||
                dateRange.to) && (
                <button
                  onClick={clearAllFilters}
                  className="flex items-center space-x-1 px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <XIcon />
                  <span>Clear All</span>
                </button>
              )}
            </div>
          </div>

          {/* Main Filter Bar */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Primary Filters */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">
                  Quick Filters:
                </span>
              </div>

              {/* Sector Dropdown */}
              <div className="relative" ref={ref1}>
                <button
                  className="modern-dropdown-button"
                  type="button"
                  aria-expanded={sector.drop}
                  aria-haspopup="listbox"
                  aria-label="Select technology sector"
                  onClick={() => {
                    setSector({ ...sector, drop: !sector.drop });
                    setSubSector({ ...subSector, drop: false });
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      setSector({ ...sector, drop: !sector.drop });
                      setSubSector({ ...subSector, drop: false });
                    }
                  }}
                >
                  <span className="truncate">
                    {sector.id === "" ? "All Sectors" : sector.selected}
                  </span>
                  <ChevronDownIcon />
                </button>
                {sector.drop && (
                  <div
                    className="modern-dropdown-content"
                    role="listbox"
                    aria-label="Technology sectors"
                  >
                    <ul className="py-2">
                      <li role="option" aria-selected={sector.id === ""}>
                        <button
                          className="modern-dropdown-item"
                          onClick={() => {
                            setSector({
                              id: "",
                              drop: false,
                              selected: "All Sectors",
                            });
                            setSubSector({
                              ...subSector,
                              id: "",
                              drop: false,
                              count: 0,
                            });
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              e.preventDefault();
                              setSector({
                                id: "",
                                drop: false,
                                selected: "All Sectors",
                              });
                              setSubSector({
                                ...subSector,
                                id: "",
                                drop: false,
                                count: 0,
                              });
                            }
                          }}
                        >
                          <span className="flex items-center">
                            <div
                              className="w-2 h-2 bg-gray-400 rounded-full mr-3"
                              aria-hidden="true"
                            ></div>
                            All Sectors
                          </span>
                        </button>
                      </li>
                      {sectorlist &&
                        sectorlist.SECTOR_LIST.map((item: sectorItem, id) => (
                          <li
                            key={id}
                            role="option"
                            aria-selected={sector.id === item._id}
                          >
                            <button
                              className="modern-dropdown-item"
                              onClick={() => {
                                handleSectorClick(item._id, item.name);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter" || e.key === " ") {
                                  e.preventDefault();
                                  handleSectorClick(item._id, item.name);
                                }
                              }}
                            >
                              <span className="flex items-center">
                                <div
                                  className="w-2 h-2 bg-GTI-BLUE-default rounded-full mr-3"
                                  aria-hidden="true"
                                ></div>
                                {item.name}
                              </span>
                            </button>
                          </li>
                        ))}
                    </ul>
                  </div>
                )}
              </div>
              {/* Subsector Dropdown */}
              {subsectorlist.SUB_SECTOR_LIST.length > 0 &&
                sector.selected !== "All Sectors" &&
                sector.id !== "" && (
                  <div className="relative" ref={ref2}>
                    <button
                      className="modern-dropdown-button"
                      type="button"
                      onClick={() => {
                        setSector({ ...sector, drop: false });
                        setSubSector((prev) => ({ ...prev, drop: !prev.drop }));
                      }}
                    >
                      <span className="truncate">
                        {sector.id === ""
                          ? "Select Subsector"
                          : subSector.selected}
                      </span>
                      <ChevronDownIcon />
                    </button>
                    {subSector.drop && (
                      <div className="modern-dropdown-content">
                        <ul className="py-2">
                          {subsectorlist.SUB_SECTOR_LIST.map(
                            (item: subsectorItem, id) => (
                              <li key={id}>
                                <button
                                  className="modern-dropdown-item"
                                  onClick={() => {
                                    setSubSector({
                                      id: item._id,
                                      drop: false,
                                      selected: item.name,
                                      count: getCountbyFilter(),
                                    });
                                    handleSubSectorClick();
                                  }}
                                >
                                  <span className="flex items-center">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                    {item.name}
                                  </span>
                                </button>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
            </div>

            {/* Search Toggle and Section */}
            <div className="flex-1 max-w-md lg:max-w-lg">
              <div className="flex items-center gap-2 mb-3">
                <button
                  onClick={() => setUseAdvancedSearch(!useAdvancedSearch)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors duration-200 ${
                    useAdvancedSearch
                      ? "bg-GTI-BLUE-default text-white"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  }`}
                >
                  {useAdvancedSearch ? "Advanced Search" : "Basic Search"}
                </button>
                <span className="text-xs text-gray-500">
                  {useAdvancedSearch ? "Switch to basic" : "Switch to advanced"}
                </span>
              </div>

              {!useAdvancedSearch ? (
                <>
                  <label htmlFor="technology-search" className="sr-only">
                    Search technologies
                  </label>
                  <div className="relative">
                    <div
                      className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                      aria-hidden="true"
                    >
                      <SearchIcon />
                    </div>
                    <input
                      id="technology-search"
                      type="text"
                      className="modern-search-input"
                      placeholder="Search technologies, companies, or keywords..."
                      value={search}
                      onChange={(e) => handleSearch(e.target.value)}
                      onFocus={() =>
                        setShowSuggestions(searchSuggestions.length > 0)
                      }
                      onBlur={() =>
                        setTimeout(() => setShowSuggestions(false), 200)
                      }
                      aria-label="Search technologies, companies, or keywords"
                      aria-describedby={
                        search ? "search-results-count" : undefined
                      }
                      autoComplete="off"
                    />

                    {/* Search Suggestions Dropdown */}
                    {showSuggestions && searchSuggestions.length > 0 && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-60 overflow-auto">
                        <ul className="py-2">
                          {searchSuggestions.map((suggestion, index) => (
                            <li key={index}>
                              <button
                                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 hover:text-GTI-BLUE-default transition-colors duration-150 flex items-center"
                                onClick={() => {
                                  handleSearch(suggestion);
                                  setShowSuggestions(false);
                                }}
                              >
                                <SearchIcon />
                                <span className="ml-2">{suggestion}</span>
                              </button>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {search && (
                      <button
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => {
                          handleSearch("");
                          setShowSuggestions(false);
                        }}
                        aria-label="Clear search"
                        type="button"
                      >
                        <svg
                          className="w-4 h-4 text-gray-400 hover:text-gray-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                  {search && (
                    <div
                      id="search-results-count"
                      className="sr-only"
                      aria-live="polite"
                    >
                      Search results updated for "{search}"
                    </div>
                  )}

                  {/* Save Search and Saved Searches */}
                  <div className="flex items-center justify-between mt-3">
                    {search && (
                      <button
                        onClick={saveCurrentSearch}
                        className="text-xs text-GTI-BLUE-default hover:text-blue-700 flex items-center"
                      >
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                          />
                        </svg>
                        Save Search
                      </button>
                    )}

                    {savedSearches.length > 0 && (
                      <button
                        onClick={() => setShowSavedSearches(!showSavedSearches)}
                        className="text-xs text-gray-600 hover:text-gray-800 flex items-center"
                      >
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                          />
                        </svg>
                        Saved ({savedSearches.length})
                      </button>
                    )}
                  </div>

                  {/* Saved Searches Dropdown */}
                  {showSavedSearches && savedSearches.length > 0 && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                      <div className="text-xs font-medium text-gray-700 mb-2">
                        Saved Searches:
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {savedSearches.map((savedSearch, index) => (
                          <div
                            key={index}
                            className="flex items-center bg-white rounded-full px-3 py-1 text-xs"
                          >
                            <button
                              onClick={() => {
                                handleSearch(savedSearch);
                                setShowSavedSearches(false);
                              }}
                              className="text-GTI-BLUE-default hover:text-blue-700 mr-2"
                            >
                              {savedSearch}
                            </button>
                            <button
                              onClick={() => removeSavedSearch(savedSearch)}
                              className="text-gray-400 hover:text-red-500"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="mt-4">
                  <AdvancedSearch
                    items={searchableProducts}
                    onResultsChange={handleSearchResults}
                    placeholder="Search technologies by name, company, description, or tags..."
                    categories={categories}
                    statuses={statuses}
                    availableTags={availableTags}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Advanced Filters Panel */}
          {showAdvancedFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Development Stage Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Development Stage
                  </label>
                  <select
                    value={developmentStageFilter}
                    onChange={(e) => setDevelopmentStageFilter(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default"
                  >
                    <option value="">All Stages</option>
                    {developmentStages.map((stage) => (
                      <option key={stage} value={stage}>
                        {stage}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Date Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <div className="flex space-x-2">
                    <div className="flex-1">
                      <input
                        type="date"
                        value={dateRange.from}
                        onChange={(e) =>
                          setDateRange({ ...dateRange, from: e.target.value })
                        }
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default"
                        placeholder="From"
                      />
                    </div>
                    <div className="flex-1">
                      <input
                        type="date"
                        value={dateRange.to}
                        onChange={(e) =>
                          setDateRange({ ...dateRange, to: e.target.value })
                        }
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default"
                        placeholder="To"
                      />
                    </div>
                  </div>
                </div>

                {/* Quick Filter Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quick Filters
                  </label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => handleSearch("AI")}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-GTI-BLUE-default hover:text-white transition-colors"
                    >
                      AI & Machine Learning
                    </button>
                    <button
                      onClick={() => handleSearch("sustainable")}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-GTI-BLUE-default hover:text-white transition-colors"
                    >
                      Sustainable Tech
                    </button>
                    <button
                      onClick={() => handleSearch("blockchain")}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-GTI-BLUE-default hover:text-white transition-colors"
                    >
                      Blockchain
                    </button>
                    <button
                      onClick={() => handleSearch("IoT")}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-GTI-BLUE-default hover:text-white transition-colors"
                    >
                      IoT
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Active Filters Display */}
          {(sector.id !== "" ||
            subSector.id !== "" ||
            search ||
            developmentStageFilter ||
            dateRange.from ||
            dateRange.to) && (
            <div className="flex flex-wrap items-center gap-2 mt-4 pt-4 border-t border-gray-100">
              <span className="text-sm text-gray-600">Active filters:</span>
              {sector.id !== "" && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-GTI-BLUE-default text-white">
                  {sector.selected}
                  <button
                    className="ml-2 hover:text-gray-200"
                    onClick={() => {
                      setSector({
                        id: "",
                        drop: false,
                        selected: "All Sectors",
                      });
                      setSubSector({
                        ...subSector,
                        id: "",
                        drop: false,
                        count: 0,
                      });
                    }}
                  >
                    ×
                  </button>
                </span>
              )}
              {subSector.id !== "" && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                  {subSector.selected}
                  <button
                    className="ml-2 hover:text-gray-200"
                    onClick={() =>
                      setSubSector({
                        ...subSector,
                        id: "",
                        drop: false,
                        selected: "All Sub-Sectors",
                      })
                    }
                  >
                    ×
                  </button>
                </span>
              )}
              {search && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-500 text-white">
                  Search: "{search}"
                  <button
                    className="ml-2 hover:text-gray-200"
                    onClick={() => handleSearch("")}
                  >
                    ×
                  </button>
                </span>
              )}
              {developmentStageFilter && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
                  Stage: {developmentStageFilter}
                  <button
                    className="ml-2 hover:text-gray-200"
                    onClick={() => setDevelopmentStageFilter("")}
                  >
                    ×
                  </button>
                </span>
              )}
              {(dateRange.from || dateRange.to) && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-500 text-white">
                  Date: {dateRange.from || "Start"} - {dateRange.to || "End"}
                  <button
                    className="ml-2 hover:text-gray-200"
                    onClick={() => setDateRange({ from: "", to: "" })}
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          )}
        </div>
      </div>
      {/* Product List Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8">
        <ProductList
          skip={page.skip}
          limit={LIMIT}
          secId={sector.id}
          subSecId={subSector.id}
          search={search}
          sortBy={sortBy}
          developmentStageFilter={developmentStageFilter}
          dateRange={dateRange}
        />
      </div>

      {/* Modern Pagination */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              disabled={page.skip === "0"}
              onClick={() => fetchData(-1)}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              disabled={
                (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                products.TOTAL
              }
              onClick={() => fetchData(1)}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>

          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {parseInt(page.skip) * parseInt(page.limit) + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    (parseInt(page.skip) + 1) * parseInt(page.limit),
                    products.TOTAL
                  )}
                </span>{" "}
                of <span className="font-medium">{products.TOTAL}</span> results
              </p>
            </div>

            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  disabled={page.skip === "0"}
                  onClick={() => fetchData(-1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                {/* Page Numbers */}
                {Array.from({ length: Math.min(5, maxSkip) }, (_, i) => {
                  const pageNum = Math.max(1, parseInt(page.skip) - 2) + i;
                  if (pageNum > maxSkip) return null;

                  return (
                    <button
                      key={pageNum}
                      onClick={() =>
                        fetchData(pageNum - parseInt(page.skip) - 1)
                      }
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === parseInt(page.skip) + 1
                          ? "z-10 bg-GTI-BLUE-default border-GTI-BLUE-default text-white"
                          : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  disabled={
                    (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                    products.TOTAL
                  }
                  onClick={() => fetchData(1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;
