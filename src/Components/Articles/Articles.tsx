import React, { Dispatch, useEffect, useState, useMemo } from "react";
import { Helmet } from "react-helmet";
import { BookOpenIcon, SparklesIcon } from "@heroicons/react/24/solid";

import article_icon from "../../assests/images/article/article_icon.png";
import { SKIP, LIMIT, title, metaData } from "../constants";
import ArticlesList from "./ArticlesList";
import { useDispatch, useSelector } from "react-redux";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSector } from "../../store/actioncreators/sub-sectoractions";
import ModernSectorFilter from "./ModernSectorFilter";
import AdvancedSearch from "../Profile/AdvancedSearch";
import "./style.css";

const Articles = () => {
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);

  const dispatch: Dispatch<any> = useDispatch();
  const [page] = useState({
    skip: SKIP,
    limit: LIMIT,
  });
  const [selectedSector, setSelectedSector] = useState({
    id: "",
    selected: "All Sectors",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [useAdvancedSearch, setUseAdvancedSearch] = useState(false);
  const [filteredResults, setFilteredResults] = useState<any[]>([]);

  // Get articles from state
  const articles: ARTICLE = useSelector(
    (state: STATE) => state.ARTICLE.ARTICLE
  );

  // Transform articles data for AdvancedSearch component
  const searchableArticles = useMemo(() => {
    if (!articles?.articles) return [];

    return articles.articles.map((item: any) => ({
      id: item.id || item._id,
      title: item.topic || item.title,
      description: item.description || item.content || "",
      category: item.sectorId || "General",
      status: item.status || "Published",
      date: new Date(item.createdAt || item.publishedAt || Date.now()),
      tags: [
        item.sectorId,
        item.author,
        ...(item.keywords || []),
        ...(item.tags || []),
      ].filter(Boolean),
      originalData: item,
    }));
  }, [articles]);

  // Categories for filtering
  const categories = useMemo(() => {
    const sectors = sectorlist?.SECTOR_LIST || [];
    return sectors.map((sector: any) => ({
      value: sector.id,
      label: sector.name,
    }));
  }, [sectorlist]);

  // Status options for filtering
  const statuses = [
    { value: "Published", label: "Published" },
    { value: "Draft", label: "Draft" },
    { value: "Featured", label: "Featured" },
    { value: "Archived", label: "Archived" },
  ];

  // Available tags for filtering
  const availableTags = useMemo(() => {
    const allTags = searchableArticles.flatMap((item) => item.tags);
    return Array.from(new Set(allTags)).sort();
  }, [searchableArticles]);

  const handleSearchResults = (results: any[]) => {
    setFilteredResults(results);
  };

  // Initialize filtered results when searchable articles change
  useEffect(() => {
    if (useAdvancedSearch) {
      setFilteredResults(searchableArticles);
    }
  }, [searchableArticles, useAdvancedSearch]);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      dispatch(getSector());
      dispatch(getSubSector());
      // Simulate loading time for better UX
      setTimeout(() => setIsLoading(false), 1000);
    };

    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSectorChange = (sector: { id: string; selected: string }) => {
    setSelectedSector(sector);
  };

  return (
    <div className="flex flex-col relative min-h-screen w-full bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <Helmet>
        <title>{title.ARTICLES}</title>
        <meta
          name="description"
          key="description"
          content={metaData.ARTICLES}
        />
        <meta name="title" key="title" content="Articles" />
        <meta property="og:title" content="Articles" />
        <meta property="og:description" content={metaData.ARTICLES} />
        <meta property="og:image" content={article_icon} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/articles`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Articles" />
        <meta name="twitter:description" content={metaData.ARTICLES} />
        <meta name="twitter:image" content={article_icon} />
        <meta name="twitter:card" content="Articles" />
      </Helmet>

      {/* Modern Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-GTI-BLUE-default via-blue-700 to-indigo-800">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
          <div className="text-center">
            <div className="flex justify-center items-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                  <BookOpenIcon className="h-12 w-12 md:h-16 md:w-16 text-white" />
                </div>
              </div>
            </div>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
              <span className="block">Knowledge</span>
              <span className="block bg-gradient-to-r from-blue-200 to-cyan-200 bg-clip-text text-transparent">
                Articles
              </span>
            </h1>

            <p className="max-w-3xl mx-auto text-lg md:text-xl text-blue-100 leading-relaxed mb-8">
              Discover cutting-edge insights and industry expertise through our
              comprehensive collection of articles covering cleantech, biotech,
              and ICT innovations across global markets.
            </p>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-blue-200">
              <div className="flex items-center gap-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Expert Insights</span>
              </div>
              <div className="flex items-center gap-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Industry Trends</span>
              </div>
              <div className="flex items-center gap-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Innovation Stories</span>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl"></div>
        </div>
      </div>

      {/* Modern Filter Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col sm:flex-row gap-6 items-start sm:items-center justify-between">
          {/* Section Header */}
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-bold text-gray-900">
              Browse Articles
            </h2>
            <span className="px-3 py-1 bg-GTI-BLUE-default/10 text-GTI-BLUE-default text-sm font-medium rounded-full">
              {sectorlist.SECTOR_LIST
                ? `${sectorlist.SECTOR_LIST.length + 1} sectors`
                : "Loading..."}
            </span>
          </div>

          {/* Enhanced Sector Filter */}
          <div className="w-full sm:w-auto">
            <ModernSectorFilter
              sectors={sectorlist.SECTOR_LIST || []}
              selectedSector={selectedSector}
              onSectorChange={handleSectorChange}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Advanced Search Section */}
        <div className="mt-8">
          <div className="flex items-center gap-2 mb-4">
            <button
              onClick={() => setUseAdvancedSearch(!useAdvancedSearch)}
              className={`px-4 py-2 text-sm rounded-lg transition-colors duration-200 ${
                useAdvancedSearch
                  ? "bg-GTI-BLUE-default text-white"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              {useAdvancedSearch
                ? "Advanced Search Active"
                : "Enable Advanced Search"}
            </button>
            <span className="text-sm text-gray-500">
              {useAdvancedSearch
                ? "Switch to basic filtering"
                : "Search with filters, tags, and sorting"}
            </span>
          </div>

          {useAdvancedSearch && (
            <AdvancedSearch
              items={searchableArticles}
              onResultsChange={handleSearchResults}
              placeholder="Search articles by title, content, author, or tags..."
              categories={categories}
              statuses={statuses}
              availableTags={availableTags}
            />
          )}
        </div>

        {/* Filter Summary */}
        {selectedSector.id && (
          <div className="mt-4 flex items-center gap-2 text-sm text-gray-600">
            <span>Filtering by:</span>
            <span className="inline-flex items-center gap-1 px-3 py-1 bg-GTI-BLUE-default/10 text-GTI-BLUE-default rounded-full font-medium">
              {selectedSector.selected}
              <button
                onClick={() =>
                  handleSectorChange({ id: "", selected: "All Sectors" })
                }
                className="ml-1 hover:text-red-600 transition-colors duration-150"
                title="Clear filter"
              >
                ×
              </button>
            </span>
          </div>
        )}
      </div>
      <div className="w-full">
        <ArticlesList
          skip={page.skip}
          limit={page.limit}
          secId={selectedSector.id}
        />
      </div>
    </div>
  );
};

export default Articles;
