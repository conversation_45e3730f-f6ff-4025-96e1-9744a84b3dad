import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import "./style.css";
import * as ROUTE from "../Constants/routes";
import {
  HEADER_ARTICLES,
  HEADER_PUBLICATIONS,
  HEADER_EVENTS,
  HEADER_HELPDESK,
  HEADER_INNOVATIONCALL,
  HEADER_OPPORTUNITES,
  HEADER_TECHNOLOGIES,
  HELPDESK,
  HOME,
  INNOVATION,
  OPPORTUNITY,
  TECHNOLOGY,
  HEADER_NEWS,
  HEADER_PREMIUM_SERVICES_DISPLAYER,
  HEADER_PREMIUM_SERVICES_SCOUTER,
} from "../constants";
import { useDetectClickOutside } from "react-detect-click-outside";

interface IHMid {
  handleShow?: any;
  show?: boolean;
}

const H_Mid: React.FC<IHMid> = ({ handleShow, show }) => {
  const location = useLocation();
  const [mediaDrop, setMediaDrop] = useState(false);
  const [premiumServicesDrop, setPremiumServicesDrop] = useState(false);
  const [mediaTimeout, setMediaTimeout] = useState<NodeJS.Timeout | null>(null);
  const [servicesTimeout, setServicesTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const media_ref = useDetectClickOutside({
    onTriggered: () => setMediaDrop(false),
  });
  const premium_services_ref = useDetectClickOutside({
    onTriggered: () => setPremiumServicesDrop(false),
  });

  const handleMediaEnter = () => {
    if (mediaTimeout) {
      clearTimeout(mediaTimeout);
      setMediaTimeout(null);
    }
    setMediaDrop(true);
  };

  const handleMediaLeave = () => {
    const timeout = setTimeout(() => {
      setMediaDrop(false);
    }, 150); // Small delay to prevent accidental closing
    setMediaTimeout(timeout);
  };

  const handleServicesEnter = () => {
    if (servicesTimeout) {
      clearTimeout(servicesTimeout);
      setServicesTimeout(null);
    }
    setPremiumServicesDrop(true);
  };

  const handleServicesLeave = () => {
    const timeout = setTimeout(() => {
      setPremiumServicesDrop(false);
    }, 150); // Small delay to prevent accidental closing
    setServicesTimeout(timeout);
  };

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (mediaTimeout) clearTimeout(mediaTimeout);
      if (servicesTimeout) clearTimeout(servicesTimeout);
    };
  }, [mediaTimeout, servicesTimeout]);

  return (
    <>
      {(mediaDrop || premiumServicesDrop) && (
        <div className="b2match-dropdown-overlay" />
      )}
      <div className="modern-nav-menu">
        <ul className="modern-nav-list">
          <li className="modern-nav-item">
            <Link
              className={`modern-nav-link ${
                location.pathname === TECHNOLOGY ? "active" : ""
              }`}
              to={TECHNOLOGY}
              onClick={() => {
                if (show) handleShow();
              }}
            >
              {HEADER_TECHNOLOGIES}
            </Link>
          </li>
          <li className="b2match-nav-item">
            <Link
              className={`b2match-nav-link ${
                location.pathname === OPPORTUNITY ? "active" : ""
              }`}
              onClick={() => {
                if (show) handleShow();
              }}
              to={OPPORTUNITY}
            >
              {HEADER_OPPORTUNITES}
            </Link>
          </li>
          <li className="b2match-nav-item">
            <Link
              className={`b2match-nav-link ${
                location.pathname === INNOVATION ? "active" : ""
              }`}
              onClick={() => {
                if (show) handleShow();
              }}
              to={INNOVATION}
            >
              {HEADER_INNOVATIONCALL}
            </Link>
          </li>
          <li className="b2match-nav-item">
            <Link
              className={`b2match-nav-link ${
                location.pathname === HELPDESK ? "active" : ""
              }`}
              onClick={() => {
                if (show) handleShow();
              }}
              to={HELPDESK}
            >
              {HEADER_HELPDESK}
            </Link>
          </li>
          {/* <li>
          <Link
            className={`item ${
              location.pathname === "/masterclass" ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to="/masterclass"
          >
            Masterclass
          </Link>
        </li> */}
          <li
            ref={media_ref}
            className="b2match-dropdown-container"
            onMouseEnter={handleMediaEnter}
            onMouseLeave={handleMediaLeave}
          >
            <button
              id="media_drop"
              className="b2match-dropdown-button"
              type="button"
            >
              <span className="b2match-dropdown-text">Media</span>
              <svg
                className={`b2match-dropdown-arrow ${
                  mediaDrop ? "rotate-180" : "rotate-0"
                }`}
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="media_dropdown"
              className={`b2match-dropdown ${mediaDrop ? "show" : "hide"}`}
            >
              <div className="b2match-dropdown-menu">
                <div className="b2match-dropdown-grid">
                  <div className="b2match-dropdown-section">
                    <h3 className="b2match-dropdown-header">Success stories</h3>
                    <Link
                      to={ROUTE.ARTICLE}
                      onClick={() => {
                        setMediaDrop(false);
                        if (show) handleShow();
                      }}
                      className="b2match-dropdown-item"
                    >
                      <div className="b2match-dropdown-icon">📰</div>
                      <div className="b2match-dropdown-content">
                        <div className="b2match-dropdown-title">
                          {HEADER_ARTICLES}
                        </div>
                        <div className="b2match-dropdown-description">
                          Success stories of networking and business matchmaking
                          events
                        </div>
                      </div>
                    </Link>
                  </div>
                  <div className="b2match-dropdown-section">
                    <h3 className="b2match-dropdown-header">Product news</h3>
                    <Link
                      to={ROUTE.NEWS}
                      onClick={() => {
                        setMediaDrop(false);
                        if (show) handleShow();
                      }}
                      className="b2match-dropdown-item"
                    >
                      <div className="b2match-dropdown-icon">📢</div>
                      <div className="b2match-dropdown-content">
                        <div className="b2match-dropdown-title">
                          {HEADER_NEWS}
                        </div>
                        <div className="b2match-dropdown-description">
                          Read about product updates and announcements
                        </div>
                      </div>
                    </Link>
                  </div>
                  <div className="b2match-dropdown-section">
                    <h3 className="b2match-dropdown-header">Blog</h3>
                    <Link
                      to={ROUTE.PUBLICATIONS}
                      onClick={() => {
                        setMediaDrop(false);
                        if (show) handleShow();
                      }}
                      className="b2match-dropdown-item"
                    >
                      <div className="b2match-dropdown-icon">📚</div>
                      <div className="b2match-dropdown-content">
                        <div className="b2match-dropdown-title">
                          {HEADER_PUBLICATIONS}
                        </div>
                        <div className="b2match-dropdown-description">
                          Read from experts about event networking and B2B
                          matchmaking
                        </div>
                      </div>
                    </Link>
                  </div>
                  <div className="b2match-dropdown-section">
                    <h3 className="b2match-dropdown-header">Events</h3>
                    <Link
                      to={ROUTE.EVENTS}
                      onClick={() => {
                        setMediaDrop(false);
                        if (show) handleShow();
                      }}
                      className="b2match-dropdown-item"
                    >
                      <div className="b2match-dropdown-icon">🎯</div>
                      <div className="b2match-dropdown-content">
                        <div className="b2match-dropdown-title">
                          {HEADER_EVENTS}
                        </div>
                        <div className="b2match-dropdown-description">
                          Discover upcoming events and networking opportunities
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </li>
          <li
            ref={premium_services_ref}
            className="b2match-dropdown-container"
            onMouseEnter={handleServicesEnter}
            onMouseLeave={handleServicesLeave}
          >
            <button
              id="premium_services_drop"
              className="b2match-dropdown-button"
              type="button"
            >
              <span className="b2match-dropdown-text">GTI Services</span>
              <svg
                className={`b2match-dropdown-arrow ${
                  premiumServicesDrop ? "rotate-180" : "rotate-0"
                }`}
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="premium_services_drop"
              className={`b2match-dropdown ${
                premiumServicesDrop ? "show" : "hide"
              }`}
            >
              <div className="b2match-dropdown-menu">
                <div className="b2match-dropdown-header-section">
                  <h2 className="b2match-dropdown-main-title">
                    GTI Market Access & Innovation Services
                  </h2>
                  <p className="b2match-dropdown-main-subtitle">
                    Comprehensive technology solutions for displayers and
                    scouters
                  </p>
                </div>
                <div className="b2match-dropdown-grid-two-col">
                  <Link
                    to={ROUTE.DISPLAYER_PREMIUM_SERVICES}
                    onClick={() => {
                      setPremiumServicesDrop(false);
                      if (show) handleShow();
                    }}
                    className="b2match-dropdown-item"
                  >
                    <div className="b2match-dropdown-icon">🎨</div>
                    <div className="b2match-dropdown-content">
                      <div className="b2match-dropdown-title">
                        Technology Showcase
                      </div>
                      <div className="b2match-dropdown-description">
                        Comprehensive support for technology companies to deploy
                        and validate their technology in new markets
                      </div>
                    </div>
                  </Link>
                  <Link
                    to={ROUTE.SCOUTER_PREMIUM_SERVICES}
                    onClick={() => {
                      setPremiumServicesDrop(false);
                      if (show) handleShow();
                    }}
                    className="b2match-dropdown-item"
                  >
                    <div className="b2match-dropdown-icon">🔍</div>
                    <div className="b2match-dropdown-content">
                      <div className="b2match-dropdown-title">
                        Technology Scouting
                      </div>
                      <div className="b2match-dropdown-description">
                        Customized assistance for finding technology partners
                        for collaborations or investments
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </>
  );
};

export default H_Mid;
