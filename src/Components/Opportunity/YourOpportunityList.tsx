import React, { Di<PERSON>atch, useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  APPROVED,
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  FILE_PATH,
  FILE_TYPE,
  NONE,
  oppotunityItemPartialFetched,
  oppotunityUpdateItem,
  PENDING,
  presignedData,
  REJECTED,
  sectorItem,
  subsectorItem,
  YOUR_OPP,
} from "../constants";
import { useNavigate } from "react-router-dom";
import { MdOutlineEdit } from "react-icons/md";
import axios from "axios";
import oppbanner from "../../assests/images/product_alt.png";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { Formik, Form } from "formik";
import {
  getYourOpportunities,
  updateOppotunity,
} from "../../store/actioncreators/opportunityactions";
import { oppSchema } from "../validations/oppValidations";
import { ScreenSpinner } from "../utils/loader";
import RenderHTML from "../utils/RenderHTML";
import { Editor } from "@tinymce/tinymce-react";
import { RequestMethods } from "../../shared/RequestMethods";
import CustomEditor from "../shared/CustomEditor";

interface MyFormValues {
  tech_require: string;
  description: string;
  sectorId: string;
  subSectorId: string;
}

type files = {
  image: Boolean;
  document: Boolean;
  imageFile: File | null;
  documentFiles: FileList | null;
};

type productModal = {
  tech_require: string;
  description: string;
  document: FileList | null;
  image: File | null;
  sectorId: string;
  subSectorId: string;
};

const OppModal = ({
  currentOpp,
  handleModal,
}: {
  currentOpp: oppotunityItemPartialFetched;
  handleModal: () => void;
}) => {
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );

  const initialValues: MyFormValues = {
    tech_require: currentOpp.technologyPartnerRequirement,
    description: currentOpp.description,
    sectorId: currentOpp.sectorId,
    subSectorId: currentOpp.subSectorId,
  };

  const dispatch: Dispatch<any> = useDispatch();

  const [files, setFiles] = useState<files>({
    image: false,
    document: false,
    imageFile: null,
    documentFiles: null,
  });

  const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setFiles({ ...files, imageFile: fileList[0], image: false });
  };

  const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;
    setFiles({ ...files, documentFiles: fileList, document: false });
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: RequestMethods.POST,
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: files.imageFile,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const postDocument = async (signed: string, file: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE_DOC,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const handleCreate = async (values: MyFormValues) => {
    let signedLogoURL: string = "";
    let signedDocumentURLWhole: string = "";

    if (files.imageFile) {
      const signedLogoData: presignedData = {
        fileName: files.imageFile.name,
        filePath: FILE_PATH.PRODUCTS_IMAGE,
        fileType: FILE_TYPE.PNG,
      };
      signedLogoURL = await getPresigned(signedLogoData);
      await postLogo(signedLogoURL);
    }

    if (files.documentFiles) {
      Array.from(files.documentFiles).forEach(async (document, i) => {
        let signedDocumentData: presignedData = {
          fileName: document.name,
          filePath: FILE_PATH.COMPANY_DOCS,
          fileType: FILE_TYPE.PDF,
        };
        let tempurl = (await getPresigned(signedDocumentData)) + " ";
        signedDocumentURLWhole = tempurl.split("?")[0] + " ";
        postDocument(tempurl, document);
      });
    }
    setTimeout(() => {
      const data: oppotunityUpdateItem = {
        opportunityId: currentOpp._id,
        technologyPartnerRequirement: values.tech_require,
        description: values.description,
        document: files.documentFiles
          ? signedDocumentURLWhole
          : currentOpp.document,
        image: files.imageFile ? signedLogoURL.split("?")[0] : currentOpp.image,
        sectorId: values.sectorId,
        subSectorId: values.subSectorId,
      };
      dispatch(updateOppotunity(data));
      handleModal();
    }, 2000);
  };
  const content = (
    <div className="z-10 pb-[200px] pt-4 fixed w-full h-screen bg-slate-700 bg-opacity-70 top-0 left-0 flex justify-center overflow-auto">
      <div className="product-modal-main relative">
        <div className="flex">
          <h4 className="text-lg font-roboto">Edit</h4>
          <button
            onClick={() => {
              handleModal();
            }}
            className="absolute right-0 top-0 font-bold hover:text-red-500 duration-300 border border-slate-100 px-3 py-1 rounded"
          >
            X
          </button>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={oppSchema}
          onSubmit={(values) => handleCreate(values)}
        >
          {({ handleChange, setFieldValue, handleSubmit, errors, values }) => (
            <>
              <Form className="flex flex-col w-full space-y-4 justify-center items-center">
                <div className="flex flex-col w-full space-x-2 relative">
                  <div className="relative">
                    <textarea
                      defaultValue={currentOpp.technologyPartnerRequirement}
                      onChange={(e) =>
                        setFieldValue("tech_require", e.target.value)
                      }
                      id="floating_outlined"
                      className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" "
                    />
                    <label
                      htmlFor="floating_outlined"
                      className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                    >
                      Technology Requirement
                    </label>
                  </div>
                  {errors.tech_require && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.tech_require}
                    </p>
                  )}
                </div>
                <div className="flex flex-col w-full space-x-2 relative">
                  <div className="relative">
                    <CustomEditor
                      onChange={(content: string) => {
                        setFieldValue("description", content);
                      }}
                    />
                    <label
                      htmlFor="floating_outlined"
                      className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                    >
                      Opportunity Description
                    </label>
                  </div>
                  {errors.description && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.description}
                    </p>
                  )}
                </div>

                <div className="flex flex-col w-full">
                  <div className="flex flex-row w-full space-x-5 items-center">
                    <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                      Sector Type:
                    </h3>
                    <select
                      onChange={(e) =>
                        setFieldValue("sectorId", e.target.value)
                      }
                      defaultValue={currentOpp.sectorId}
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                    >
                      {sectorlist.SECTOR_LIST.map((item: sectorItem, id) => {
                        return <option value={item._id}>{item.name}</option>;
                      })}
                    </select>
                  </div>
                  {errors.sectorId && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.sectorId}
                    </p>
                  )}
                </div>
                <div className="flex flex-col w-full">
                  <div className="flex flex-row w-full space-x-5 items-center">
                    <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                      Sub Sector Type:
                    </h3>
                    <select
                      onChange={(e) =>
                        setFieldValue("subsecId", e.target.value)
                      }
                      defaultValue={currentOpp.subSectorId}
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                    >
                      {subsectorlist.SUB_SECTOR_LIST.map(
                        (item: subsectorItem, id) => {
                          return <option value={item._id}>{item.name}</option>;
                        }
                      )}
                    </select>
                  </div>
                  {errors.subSectorId && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.subSectorId}
                    </p>
                  )}
                </div>
                <div className="flex flex-col w-full">
                  <label
                    className="block mb-2 text-sm font-medium text-gray-900"
                    htmlFor="logo"
                  >
                    Upload Product Image{" "}
                    <span className="text-red-500 text-xs">(.png only)</span>
                  </label>
                  <input
                    onChange={handleImage}
                    accept=".png"
                    type="file"
                    id="logo"
                    aria-label="company-logo"
                    className="modal-input"
                    placeholder="Click to upload Company's Logo"
                  />
                  <p
                    id="filled_error_help"
                    className={
                      "mt-2 text-xs text-red-600 dark:text-red-400 " +
                      (!files.image ? "hidden" : "")
                    }
                  >
                    {"Please upload product Image"}
                  </p>
                </div>
                <div className="flex flex-col w-full">
                  <label
                    className="block mb-2 text-sm font-medium text-gray-900"
                    htmlFor="documents"
                  >
                    Upload Product Documents
                    <span className="text-red-500 text-xs">(.pdf only)</span>
                  </label>
                  <input
                    onChange={handleDocuments}
                    accept=".pdf"
                    type="file"
                    id="documents"
                    multiple
                    aria-label="company-documents"
                    className="modal-input"
                    placeholder="Click to upload Document"
                  />
                  <p
                    id="filled_error_help"
                    className={
                      "mt-2 text-xs text-red-600 dark:text-red-400 " +
                      (!files.document ? "hidden" : "")
                    }
                  >
                    {"Please upload product documents"}
                  </p>
                </div>
                <button
                  type="submit"
                  onClick={() => handleSubmit}
                  className="button active"
                >
                  Update
                </button>
              </Form>
            </>
          )}
        </Formik>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

const Card = ({
  item,
  handleSelect,
}: {
  item: oppotunityItemPartialFetched;
  handleSelect: (data: oppotunityItemPartialFetched) => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    navigate(YOUR_OPP, { state: { product: item } });
  };

  const handleEdit = (e: any) => {
    e.stopPropagation();
    // Navigate to edit page with opportunity ID
    navigate(`/edit-opportunity/${item._id}`);
  };

  const getStatusColor = (item: oppotunityItemPartialFetched) => {
    if (item.isApprovedByAdmin) {
      return "bg-green-100 text-green-800";
    } else if (item.isRejected) {
      return "bg-red-100 text-red-800";
    } else {
      return "bg-yellow-100 text-yellow-800";
    }
  };

  const getStatusText = (item: oppotunityItemPartialFetched) => {
    if (item.isApprovedByAdmin) {
      return "Approved";
    } else if (item.isRejected) {
      return "Rejected";
    } else {
      return "Pending";
    }
  };

  const getSectorColor = (sectorName: string) => {
    switch (sectorName) {
      case "Biotechnology":
        return "bg-green-100 text-green-800";
      case "Clean Technology":
        return "bg-blue-100 text-blue-800";
      case "Information Technology":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const DOP = new Date(item.createdAt);

  return (
    <article
      className="product-card-main group"
      style={{ opacity: 1, visibility: "visible" }}
      onClick={handleView}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleView();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${
        item.technologyPartnerRequirement || "opportunity"
      }`}
    >
      {/* Image Section with Overlay */}
      <div className="product-card-img relative">
        <img
          src={item.image === NONE ? oppbanner : item.image}
          className="w-full h-48 object-cover rounded-t-2xl"
          alt={`Opportunity preview`}
          loading="lazy"
        />

        {/* Sector Badge */}
        <div className="absolute top-3 left-3">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSectorColor(
              item.sectors?.name || "Information Technology"
            )}`}
          >
            {item.sectors?.name || "Information Technology"}
          </span>
        </div>

        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
              item
            )}`}
          >
            {getStatusText(item)}
          </span>
        </div>

        {/* Edit Icon - Floating */}
        <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
          <button
            onClick={handleEdit}
            className="p-2.5 bg-white/90 backdrop-blur-sm text-gray-700 rounded-full shadow-lg hover:bg-white hover:shadow-xl transition-all duration-200 hover:scale-110"
            title="Edit Opportunity"
          >
            <MdOutlineEdit className="w-4 h-4" />
          </button>
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-t-2xl"></div>
      </div>

      {/* Content Section */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-roboto text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
              {item.technologyPartnerRequirement.length > 50
                ? item.technologyPartnerRequirement.substring(0, 50) + "..."
                : item.technologyPartnerRequirement}
            </h3>
            <p className="text-sm text-gray-600 mt-1 font-medium">
              Your Opportunity
            </p>
          </div>

          {/* Edit Icon in Header */}
          <button
            onClick={handleEdit}
            className="p-2 text-gray-400 hover:text-GTI-BLUE-default hover:bg-blue-50 rounded-full transition-all duration-200"
            title="Edit Opportunity"
          >
            <MdOutlineEdit className="w-5 h-5" />
          </button>
        </div>

        {/* Description */}
        <div className="flex-1 mb-4">
          <div className="text-sm text-gray-600 line-clamp-3">
            <RenderHTML
              html={
                item.description.substring(0, 120) +
                (item.description.length > 120 ? "..." : "")
              }
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center gap-1.5">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <span className="font-medium">
                {DOP.toLocaleString("default", {
                  month: "short",
                  day: "2-digit",
                  year: "numeric",
                })}
              </span>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                item.isApprovedByAdmin
                  ? "bg-green-500"
                  : item.isRejected
                  ? "bg-red-500"
                  : "bg-yellow-500"
              }`}
            ></div>
            <span className="text-sm font-medium text-gray-600">
              {getStatusText(item)}
            </span>
          </div>
        </div>
      </div>
    </article>
  );
};

const YourOpportunityList = ({
  type,
  skip,
  limit,
  secId,
  subsecId,
}: {
  type: number;
  skip: string;
  limit: string;
  subsecId: string;
  secId: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  const opp: OPP = useSelector((state: STATE) => state.OPP.OPP);
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const navigate = useNavigate();
  useEffect(() => {
    switch (type) {
      case 0:
        dispatch(getYourOpportunities(skip, limit, APPROVED));
        break;
      case 1:
        dispatch(getYourOpportunities(skip, limit, PENDING));
        break;
      default:
        dispatch(getYourOpportunities(skip, limit, REJECTED));
    }
  }, [secId, subsecId, skip, type]);

  const [oppEditModal, setModal] = useState(false);

  const [currentOpp, setOpp] = useState<oppotunityItemPartialFetched>({
    technologyPartnerRequirement: "",
    _id: "",
    description: "",
    image: "",
    displayOnHomePage: false,
    isApprovedBySubAdmin: false,
    isApprovedByAdmin: false,
    isRejected: false,
    document: "",
    sectorId: "",
    subSectorId: "",
    userId: "",
    createdAt: "",
    __v: -1,
    sectors: {
      _id: "",
      name: "",
      slug: "",
      image: "",
      createdAt: "",
      __v: -1,
    },
    subsectors: {
      _id: "",
      name: "",
      slug: "",
      sectorId: "",
      createdAt: "",
      __v: -1,
    },
    company: {
      logo: "",
      name: "",
    },
  });

  const handleModel = () => {
    setModal(!oppEditModal);
    switch (type) {
      case 0:
        dispatch(getYourOpportunities(skip, limit, APPROVED));
        break;
      case 1:
        dispatch(getYourOpportunities(skip, limit, PENDING));
        break;
      default:
        dispatch(getYourOpportunities(skip, limit, REJECTED));
    }
  };

  const handleSelect = (item: oppotunityItemPartialFetched) => {
    if (item.__v !== -1) {
      setOpp(item);
      handleModel();
    }
  };

  // Ensure data stability
  const opportunityList = opp.YOUR_OPP_LIST || [];
  const isLoading = spinner.SPINNER;
  const hasOpportunities = opportunityList.length > 0;

  // Loading Skeleton Component
  const LoadingSkeleton = () => (
    <div className="product-list-main">
      {Array.from({ length: 6 }, (_, index) => (
        <div key={index} className="product-card-main animate-pulse">
          <div className="product-card-img bg-gray-200 h-48 rounded-t-2xl"></div>
          <div className="p-6 space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  // Empty State Component
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
        <svg
          className="w-12 h-12 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        No opportunities found
      </h3>
      <p className="text-gray-600 text-center max-w-md">
        You haven't created any opportunities yet. Click "Create Opportunity" to
        get started.
      </p>
    </div>
  );

  return (
    <div className="w-full min-h-[400px]">
      {isLoading ? (
        <LoadingSkeleton />
      ) : hasOpportunities ? (
        <div className="product-list-main">
          {opportunityList.map(
            (item: oppotunityItemPartialFetched, id: number) => {
              // Ensure item has required properties
              if (!item || !item._id) return null;

              return (
                <Card
                  item={item}
                  key={`${item._id}-${id}`}
                  handleSelect={handleSelect}
                />
              );
            }
          )}
        </div>
      ) : (
        <EmptyState />
      )}

      {oppEditModal && (
        <OppModal currentOpp={currentOpp} handleModal={handleModel} />
      )}
    </div>
  );
};

export default YourOpportunityList;
